<?php

namespace App\Http\Controllers\Review;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\Listing;
use App\Models\ReviewImage;
use App\Models\ReviewReport;
use App\Models\User;
use App\Review;
use Illuminate\Http\Request;
use App\Booking;

class ReviewController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $model = str_slug('review', '-');
        if (auth()->user()->permissions()->where('name', '=', 'view-' . $model)->first() != null) {
            $request_data = $request->all();
            $keyword = $request->get('search');
            $dateRange = $request->get('date');
            $perPage = 15;

            $startDate = null;
            $endDate = null;

            if ($dateRange && strpos($dateRange, ' - ') !== false) {
                try {
                    $dateArray = explode(' - ', $dateRange);
                    if (count($dateArray) === 2) {
                        $startDate = \Carbon\Carbon::createFromFormat('m/d/Y', trim($dateArray[0]))->startOfDay()->toDateTimeString();
                        $endDate = \Carbon\Carbon::createFromFormat('m/d/Y', trim($dateArray[1]))->endOfDay()->toDateTimeString();
                    }
                } catch (\Exception $e) {
                    // If date parsing fails, ignore the date filter
                    $startDate = null;
                    $endDate = null;
                }
            }

            //$review = Review::query()->with('booking.customer');
            $review = Review::query()->with('booking.customer')
                ->whereHas('listing', function ($q) {
                    // Only include reviews where the listing exists and is not deleted
                    $q->whereNull('deleted_at');
                });

            // Keyword search filter
            if (!empty($keyword)) {
                $review->where(function ($query) use ($keyword) {
                    $query->where('comment', 'LIKE', "%$keyword%")
                        ->orWhere('rating', "$keyword")
                        ->orWhereHas('listing', function ($q) use ($keyword) {
                            $q->where('name', 'LIKE', "%$keyword%");
                        })
                        ->orWhereHas('user', function ($q) use ($keyword) {
                            $q->where('first_name', 'LIKE', "%$keyword%")->orWhere('last_name', 'LIKE', "%$keyword%");
                        })
                        ->orWhereHas('booking', function ($q) use ($keyword) {
                            $q->where('booking_number', 'LIKE', "%$keyword%");
                        });
                });
            }

            // Date range filter
            if (!empty($startDate) && !empty($endDate)) {
                $review->whereBetween('created_at', [$startDate, $endDate]);
            }

            // Customer filter (if customers are provided in the request)
            if (!empty($request->customers)) {
                $review->whereIn('user_id', $request->customers);
            }
            if (!empty($request->service_providers)) {
                $review->whereIn('provider_id', $request->service_providers);
            }
            if (!empty($request->listings)) {
                $review->whereIn('listing_id', $request->listings);
            }

            // Role-based filters
            $user = auth()->user();
            if ($user->hasRole(['user', 'sub_admin'])) {
                // No additional condition here (users and sub_admins can access all reviews)
            } elseif ($user->hasRole('service')) {
                $review->where('provider_id', $user->id);  // Only show reviews for the service provider
            } elseif ($user->hasRole('customer')) {
                $review->where('user_id', $user->id);  // Only show reviews for the customer
            }

            // Pagination
            $review = $review->latest()->paginate($perPage);

            // Review reports handling with similar role-based conditions
            //$review_reports = ReviewReport::query();
            $review_reports = ReviewReport::query()
                ->whereHas('listing', function ($q) {
                    // Only include review reports where the listing exists and is not deleted
                    $q->whereNull('deleted_at');
                });

            if ($user->hasRole(['user', 'sub_admin'])) {
                // No additional condition here for 'user' or 'sub_admin'
            } elseif ($user->hasRole('service')) {
                $review_reports->where('provider_id', $user->id);  // Only show review reports for the service provider
            } elseif ($user->hasRole('customer')) {
                $review_reports->where('user_id', $user->id);  // Only show review reports for the customer
            }

            // Additional filters for review reports
            if (!empty($request->listings)) {
                $review_reports->whereIn('listing_id', $request->listings);
            }

            if (!empty($request->customers)) {
                $review_reports->whereIn('user_id', $request->customers);
            }

            if (!empty($request->service_providers)) {
                $review_reports->whereHas("listing", function ($q) use ($request) {
                    $q->whereIn("user_id", $request->service_providers);
                });
            }

            if ($request->has('status') && ($request->status === '0' || $request->status === '1')) {
                $review_reports->where('status', (int)$request->status);
            }

            $review_reports = $review_reports->orderBy("status", "ASC")->get();

            // Fetch all users and listings
            $service_providers = User::whereHas('roles', function ($query) {
                $query->whereIn('name', ['service', 'sub_admin', "user"]);
            })->whereHas('listings', function ($query) {
                // Only users who have at least one listing
            })->get();
            $customers = User::whereHas('roles', function ($query) {
                $query->where('name', 'customer');
            })->get();
            // Filter listings based on user role
            if (auth()->user()->hasRole(['user', 'sub_admin'])) {
                $listings = Listing::get(); // Get all listings for user and sub_admin roles
            } elseif (auth()->user()->hasRole('service')) {
                $listings = Listing::where("user_id", auth()->id())->get(); // Only get listings owned by the service provider
            } else {
                $listings = Listing::get(); // Default fallback
            }

            return view('review.review.index', compact('review', "review_reports", "request_data", "service_providers", "customers", "listings"));
        }

        return response(view('403'), 403);
    }


    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('review', '-');
        if (auth()->user()->permissions()->where('name', '=', 'add-' . $model)->first() != null) {
            // Filter listings based on user role
            if (auth()->user()->hasRole(['user', 'sub_admin'])) {
                $listings = Listing::get(); // Get all listings for user and sub_admin roles
            } elseif (auth()->user()->hasRole('service')) {
                $listings = Listing::where("user_id", auth()->id())->get(); // Only get listings owned by the service provider
            } else {
                $listings = Listing::get(); // Default fallback
            }
            return view('review.review.create', compact('listings'));
        }
        return response(view('403'), 403);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('review', '-');
        if (auth()->user()->permissions()->where('name', '=', 'add-' . $model)->first() != null) {
            $this->validate($request, [
                // 'user_id' => 'required',
                'comment' => 'required'
            ]);
            $requestData = $request->all();
            $requestData['user_id'] = auth()->id();
            Review::create($requestData);
            return redirect('review/review')->with('flash_message', 'Review added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('review', '-');
        if (auth()->user()->permissions()->where('name', '=', 'view-' . $model)->first() != null) {
            $review = Review::findOrFail($id);
            return view('review.review.show', compact('review'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('review', '-');
        if (auth()->user()->permissions()->where('name', '=', 'edit-' . $model)->first() != null) {
            $review = Review::findOrFail($id);

            // Filter listings based on user role
            if (auth()->user()->hasRole(['user', 'sub_admin'])) {
                $listings = Listing::get(); // Get all listings for user and sub_admin roles
            } elseif (auth()->user()->hasRole('service')) {
                $listings = Listing::where("user_id", auth()->id())->get(); // Only get listings owned by the service provider
            } else {
                $listings = Listing::get(); // Default fallback
            }

            return view('review.review.edit', compact('review', 'listings'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('review', '-');
        if (auth()->user()->permissions()->where('name', '=', 'edit-' . $model)->first() != null) {
            $this->validate($request, [
                'comment' => 'required',
                'rating' => 'required'
            ]);
            if (!in_array($request->rating, [0.0, 0.5, 1.0, 1.5, 2.0, 2.5, 3.0, 3.5, 4.0, 4.5, 5.0])) {
                return back()->with(["type" => "error", "message" => "Rating must be in 0.0 to 5.0", "title" => "Error"]);
            }
            $requestData = $request->all();
            $review = Review::findOrFail($id);
            $review->update($requestData);

            return redirect('review/review')->with('flash_message', 'Review updated!');
        }
        return response(view('403'), 403);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('review', '-');
        if (auth()->user()->permissions()->where('name', '=', 'delete-' . $model)->first() != null) {
            $review = Review::where("id", $id)->firstOrFail();
            if ($review) {
                foreach ($review->images as $image) {
                    $this->deleteImage($image->image);
                }
                ReviewImage::where('review_id', $review->id)->delete();
                ReviewReport::where('review_id', $review->id)->delete();
                $review->delete();
            }

            return redirect('review/review')->with('flash_message', 'Review deleted!');
        }
        return response(view('403'), 403);
    }
}
