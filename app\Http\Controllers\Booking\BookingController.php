<?php

namespace App\Http\Controllers\Booking;

use App\Http\Controllers\Controller;
use App\Booking;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class BookingController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\View\View
     */

    public function index(Request $request)
    {
        // Check user permission
        $model = str_slug('booking', '-');
        $permission = auth()->user()->permissions()->where('name', '=', 'view-' . $model)->first();
        if (!$permission) {
            return response(view('403'), 403);
        }

        // Grab inputs
        $status  = $request->get('status');
        $keyword = $request->get('search');
        $dateRange = $request->get('date');
        $perPage = 15;

        // Start building the query
       // $query = Booking::query()->with(['customer', 'listing', 'listing.category', 'statusName']);
         $query = Booking::query()
        ->with(['customer', 'listing', 'listing.category', 'statusName'])
        ->whereHas('listing', function($q) {
            // Only include bookings where the listing exists and is not deleted
            $q->whereNull('deleted_at');
        });

        // 1) Filter by status
        if ($status === 'ongoing') {
            $today = Carbon::today()->toDateString();
            // "Ongoing" means check_in <= now, check_out >= now, status=0
            $query->where(DB::raw('DATE(check_in)'), '<=', $today)
                ->where(DB::raw('DATE(check_out)'), '>=', $today)
                ->where('status', 0);

        } elseif ($status === 'upcoming') {
            $query->where('check_in', '>', $today = Carbon::today()->toDateString())
                ->where('status', 0);
        } elseif ($status === 'today') {
            $today = Carbon::today()->toDateString();
            $query->where(DB::raw('DATE(check_in)'), '=', $today);
        } elseif ($status === 'completed') {
            $query->where('status', 3);
        } elseif ($status === 'cancelled') {
            $query->where('status', 7);
        }

        // 2) Apply date range filter if provided
        if (!empty($dateRange) && strpos($dateRange, ' - ') !== false) {
            try {
                $dateArray = explode(' - ', $dateRange);
                if (count($dateArray) === 2) {
                    $startDate = Carbon::createFromFormat('m/d/Y', trim($dateArray[0]))->startOfDay()->toDateString();
                    $endDate = Carbon::createFromFormat('m/d/Y', trim($dateArray[1]))->endOfDay()->toDateString();

                    // Filter bookings where check-in date is within the selected range
                    // OR check-out date is within the selected range
                    // OR booking spans the entire selected range (check-in before start and check-out after end)
                    $query->where(function($q) use ($startDate, $endDate) {
                        $q->where(function($q1) use ($startDate, $endDate) {
                            $q1->where(DB::raw('DATE(check_in)'), '>=', $startDate)
                               ->where(DB::raw('DATE(check_in)'), '<=', $endDate);
                        })->orWhere(function($q2) use ($startDate, $endDate) {
                            $q2->where(DB::raw('DATE(check_out)'), '>=', $startDate)
                               ->where(DB::raw('DATE(check_out)'), '<=', $endDate);
                        })->orWhere(function($q3) use ($startDate, $endDate) {
                            $q3->where(DB::raw('DATE(check_in)'), '<=', $startDate)
                               ->where(DB::raw('DATE(check_out)'), '>=', $endDate);
                        });
                    });
                }
            } catch (\Exception $e) {
                // If date parsing fails, ignore the date filter
            }
        }

        // 3) Apply user role based filtering
        $user = auth()->user();
        if ($user->hasRole(['user', 'sub_admin'])) {
            // No extra condition for admin users
        } elseif ($user->hasRole('service')) {
            $query->where('provider_id', $user->id);
        } else {
            // Normal user
            $query->where('user_id', $user->id);
        }

        // 4) Apply search keyword filter
        if (!empty($keyword)) {
            $query->where(function($q) use ($keyword) {
                $q->where('booking_number', 'LIKE', "%$keyword%")
                  ->orWhereHas('customer', function($q1) use ($keyword) {
                      $q1->where('first_name', 'LIKE', "%$keyword%")
                         ->orWhere('last_name', 'LIKE', "%$keyword%");
                  })
                  ->orWhereHas('listing', function($q2) use ($keyword) {
                      $q2->where('name', 'LIKE', "%$keyword%");
                  });
            });
        }

        $booking = $query->latest()->paginate($perPage)->appends($request->except('page'));

        // 5) Return the view
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'html' => view('booking.booking.partials.booking_table', compact('booking', 'status'))->render(),
                'pagination' => $booking->appends($request->except('page'))->render(),
                'status' => $status
            ]);
        }

        return view('booking.booking.index', compact('booking', 'status'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'add-' . $model)->first() != null) {
            return view('booking.booking.create');
        }
        return response(view('403'), 403);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function store(Request $request)
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'add-' . $model)->first() != null) {
            $this->validate($request, [
                'user_id' => 'required',
                'listing_id' => 'required'
            ]);
            $requestData = $request->all();

            Booking::create($requestData);
            return redirect('booking/booking')->with('flash_message', 'Booking added!');
        }
        return response(view('403'), 403);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'view-' . $model)->first() != null) {
            $booking = Booking::findOrFail($id);
            return view('booking.booking.show', compact('booking'));
        }
        return response(view('403'), 403);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     *
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'edit-' . $model)->first() != null) {
            $booking = Booking::findOrFail($id);
            return view('booking.booking.edit', compact('booking'));
        }
        return response(view('403'), 403);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Request $request, $id)
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'edit-' . $model)->first() != null) {
            $this->validate($request, [
                'user_id' => 'required',
                'listing_id' => 'required'
            ]);
            $requestData = $request->all();

            $booking = Booking::findOrFail($id);
            $booking->update($requestData);

            return redirect('booking/booking')->with('flash_message', 'Booking updated!');
        }
        return response(view('403'), 403);
    }

    /**
     * Export bookings to CSV file based on status
     *
     * @param \Illuminate\Http\Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function exportCsv(Request $request)
    {
        $model = str_slug('booking', '-');
        $permission = auth()->user()->permissions()->where('name', '=', 'view-' . $model)->first();
        if (!$permission) {
            return response(view('403'), 403);
        }

        // Get inputs from request
        $status = $request->get('status');
        $keyword = $request->get('search');
        $dateRange = $request->get('date');

        // Start building the query
        $query = Booking::query();

        // Include relationships
        // $query->with(['customer', 'listing', 'listing.category', 'statusName']);
        $query->with(['customer', 'listing', 'listing.category', 'statusName'])
          ->whereHas('listing', function($q) {
              // Only include bookings where the listing exists and is not deleted
              $q->whereNull('deleted_at');
          });

        // Filter by status
        if ($status === 'ongoing') {
            $today = Carbon::today()->toDateString();
            $query->where(DB::raw('DATE(check_in)'), '<=', $today)
                ->where(DB::raw('DATE(check_out)'), '>=', $today)
                ->where('status', 0);
        } elseif ($status === 'upcoming') {
            $query->where('check_in', '>', $today = Carbon::today()->toDateString())
                ->where('status', 0);
        } elseif ($status === 'completed') {
            $query->where('status', 3);
        } elseif ($status === 'cancelled') {
            $query->where('status', 7);
        } elseif ($status === 'today') {
            $today = Carbon::today()->toDateString();
            $query->where(DB::raw('DATE(check_in)'), '=', $today);
        }

        // Apply date range filter if provided
        if (!empty($dateRange) && strpos($dateRange, ' - ') !== false) {
            try {
                $dateArray = explode(' - ', $dateRange);
                if (count($dateArray) === 2) {
                    $startDate = Carbon::createFromFormat('m/d/Y', trim($dateArray[0]))->startOfDay()->toDateString();
                    $endDate = Carbon::createFromFormat('m/d/Y', trim($dateArray[1]))->endOfDay()->toDateString();

                    // Filter bookings where check-in date is within the selected range
                    // OR check-out date is within the selected range
                    // OR booking spans the entire selected range (check-in before start and check-out after end)
                    $query->where(function($q) use ($startDate, $endDate) {
                        $q->where(function($q1) use ($startDate, $endDate) {
                            $q1->where(DB::raw('DATE(check_in)'), '>=', $startDate)
                               ->where(DB::raw('DATE(check_in)'), '<=', $endDate);
                        })->orWhere(function($q2) use ($startDate, $endDate) {
                            $q2->where(DB::raw('DATE(check_out)'), '>=', $startDate)
                               ->where(DB::raw('DATE(check_out)'), '<=', $endDate);
                        })->orWhere(function($q3) use ($startDate, $endDate) {
                            $q3->where(DB::raw('DATE(check_in)'), '<=', $startDate)
                               ->where(DB::raw('DATE(check_out)'), '>=', $endDate);
                        });
                    });
                }
            } catch (\Exception $e) {
                // If date parsing fails, ignore the date filter
            }
        }

        // Apply search keyword filter
        if (!empty($keyword)) {
            $query->where(function($q) use ($keyword) {
                $q->where('booking_number', 'LIKE', "%$keyword%")
                  ->orWhereHas('customer', function($q1) use ($keyword) {
                      $q1->where('first_name', 'LIKE', "%$keyword%")
                         ->orWhere('last_name', 'LIKE', "%$keyword%");
                  })
                  ->orWhereHas('listing', function($q2) use ($keyword) {
                      $q2->where('name', 'LIKE', "%$keyword%");
                  });
            });
        }

        // User role based filtering
        $user = auth()->user();
        if ($user->hasRole(['user', 'sub_admin'])) {
            // No extra condition for admin users
        } elseif ($user->hasRole('service')) {
            $query->where('provider_id', $user->id);
        } else {
            // Normal user
            $query->where('user_id', $user->id);
        }

        // Get the bookings
        $bookings = $query->get();

        // Generate filename based on status
        $filename = 'bookings_' . ($status ? $status : 'all') . '_' . date('Y-m-d') . '.csv';

        // Create CSV response
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Pragma' => 'no-cache',
            'Cache-Control' => 'must-revalidate, post-check=0, pre-check=0',
            'Expires' => '0'
        ];

        $callback = function() use ($bookings) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, [
                'Booking ID',
                'Listing',
                'Customer',
                'Category',
                'Date From',
                'Until',
                'Amount',
                'Payment Method',
                'Status',
                'Created At'
            ]);

            // Add booking data
            foreach ($bookings as $booking) {
                $row = [
                    $booking->booking_number ?? '',
                    $booking->listing->name ?? '-',
                    ($booking->customer->first_name ?? '') . ' ' . ($booking->customer->last_name ?? ''),
                    $booking->listing->category->display_name ?? '-',
                    date(config('constant.date_format'), strtotime($booking->check_in)),
                    date(config('constant.date_format'), strtotime($booking->check_out == 0 ? $booking->check_in : $booking->check_out)),
                    'COP ' . number_format($booking->total_amount, 0),
                    ucfirst($booking->payment_method ?? ''),
                    $this->getStatusName($booking),
                    $booking->created_at->format('Y-m-d H:i:s')
                ];

                fputcsv($file, $row);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get the status name for a booking
     *
     * @param \App\Booking $booking
     * @return string
     */
    private function getStatusName($booking)
    {
        $checkInDate = \Carbon\Carbon::parse($booking->check_in)->toDateString();
        $checkOutDate = \Carbon\Carbon::parse($booking->check_out)->toDateString();
        $today = now()->toDateString();

        if ($checkInDate <= $today && $checkOutDate >= $today) {
            return 'On Going';
        } else {
            return $booking->statusName->name == 'Pending' ? 'Upcoming' : ($booking->statusName->name ?? '-');
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function destroy($id)
    {
        $model = str_slug('booking', '-');
        if (auth()->user()->permissions()->where('name', '=', 'delete-' . $model)->first() != null) {
            Booking::destroy($id);
            return redirect('booking/booking')->with('flash_message', 'Booking deleted!');
        }
        return response(view('403'), 403);
    }
}
