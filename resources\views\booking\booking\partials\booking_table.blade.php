<table class="table custom_table" id="myTable">
    <thead>
        <tr>
            {{-- <th scope="col">#</th> --}}
            <th scope="col">{{ __('booking_id') }}</th>
            <th scope="col">{{ __('Listing') }}</th>
            <th scope="col">{{ __('Customer') }}</th>

            {{-- <th scope="col">Listing</th> --}}
            <th scope="col">{{ __('Category') }}</th>
            <th scope="col">{{ __('date_from') }}</th>
            <th scope="col">{{ __('until') }}</th>
            @if (!auth()->user()->hasRole('service'))
                <th scope="col">{{ __('amount') }}</th>
                <th scope="col">Payment Method</th>
            @else
                <th scope="col">{{ __('Total Paid by Customer') }}</th>
                <th scope="col">{{ __('Total Payout') }}</th>
                <th scope="col">{{ __('Booked') }}</th>
            @endif
            {{-- <th scope="col">{{ __('ratings') }}</th> --}}
            <th class="d-none" scope="col">Created At</th>
            <th scope="col">{{ __('status') }}</th>
            @if (!auth()->user()->hasRole('service'))
                <th scope="col">{{ __('action') }}</th>
            @endif
        </tr>
    </thead>
    <tbody>
        @forelse ($booking as $item)
            <tr>
                {{-- <td>{{ $loop->iteration ?? $item->id }}</td> --}}
                <td>
                    <p class="">{{ $item->booking_number ?? '' }}</p>
                </td>

                {{-- <td>{{ $item->listing->name ?? '-' }}</td> --}}
                <td>
                    {{-- <p class="limit">{{ $item->listing->address->address ?? '-' }}</p> --}}
                    <p class="limit">{{ $item->listing->name ?? 'Deleted Listing' }}</p>
                </td>
                <td>
                    <p class="">{{ $item->customer->first_name ?? '' }}
                        {{ $item->customer->last_name ?? '' }}</p>
                </td>
                <td>
                    {{ $item->listing->category->display_name ?? '-' }}
                </td>
                <td>
                    {{-- @if ($item->check_out)
                        {{ $item->check_in . ' - ' . ($item->check_out ?? '') }}
                        ({{ $item->total_days ?? '-' }} days)
                    @else
                        {{ $item->check_in   }}
                    @endif --}}
                    {{ date(config('constant.date_format'), strtotime($item->check_in)) }}
                </td>
                <td>
                    {{ date(config('constant.date_format'), strtotime($item->check_out == 0 ? $item->check_in : $item->check_out)) }}
                </td>
                <td>
                    COP
                    {{ isset($item->total_amount) ? number_format($item->total_amount, 0) : '-' }}
                </td>
                @if (auth()->user()->hasRole('service'))
                    {{-- Total payout --}}
                    <td>
                        COP
                        {{-- {{ isset($item->total_amount) ? number_format($item->total_amount, 0) : '-' }} --}}
                        @php
                            $deductedAmount = ($item->total_amount ?? 0) * (($item->listing->category->tax ?? 0) / 100);
                            $totalPayout = $item->total_amount - $deductedAmount;
                        @endphp
                        {{ isset($totalPayout) ? number_format($totalPayout) : '-' }}
                    </td>
                    <td>{{ date(config('constant.date_format'), strtotime($item->created_at)) }}
                    </td>
                @else
                    <td>{{ ucfirst($item->payment_method) ?? '' }}</td>
                @endif
                {{-- <td> {{ ($item->listing->rating ?? '') == '' ? '0.0' : $item->listing->rating }} --}}
                <td class="d-none">{{ $item->created_at }}</td>
                <td>
                    <span
                        style="@if (strtolower($item->statusName->name) == 'pending') color: #ffc107; @elseif(strtolower($item->statusName->name) == 'accept' ||
                                strtolower($item->statusName->name) == 'completed' ||
                                strtolower($item->statusName->name) == 'paid') color: green; @elseif(strtolower($item->statusName->name) == 'rejected' || strtolower($item->statusName->name) == 'cancelled') color: red; @else color: #0384CC; @endif">
                        @php
                            $checkInDate = \Carbon\Carbon::parse(
                                $item->check_in,
                            )->toDateString();
                            $checkOutDate = \Carbon\Carbon::parse(
                                $item->check_out,
                            )->toDateString();
                            $today = now()->toDateString();
                            $currentTime = now();
                            $isOnGoing = false;

                            // Check if the booking is for today
                            if ($checkInDate <= $today && $checkOutDate >= $today) {
                                // For Daily bookings, this is enough to be "On Going"
                                if ($item->listing_basis != "Hourly") {
                                    $isOnGoing = true;
                                } else {
                                    // For Hourly bookings, check if current time is within any booked slot
                                    $hourlySlots = $item->hourly_slots;
                                    if ($hourlySlots && $hourlySlots->count() > 0) {
                                        foreach ($hourlySlots as $slot) {
                                            // Parse the slot time (format is typically "21:00 - 22:00")
                                            $slotParts = explode(' - ', $slot->slot);
                                            if (count($slotParts) == 2) {
                                                $slotStartTime = \Carbon\Carbon::parse($today . ' ' . $slotParts[0]);
                                                $slotEndTime = \Carbon\Carbon::parse($today . ' ' . $slotParts[1]);

                                                // Check if current time is within this slot
                                                if ($currentTime->between($slotStartTime, $slotEndTime)) {
                                                    $isOnGoing = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        @endphp
                        @if ($isOnGoing)
                            On Going
                        @else
                            {{ $item->statusName->name == 'Pending' ? 'Upcoming' : $item->statusName->name ?? '-' }}
                        @endif
                    </span>
                </td>
                @if (!auth()->user()->hasRole('service'))
                    <td class="form_btn ">
                        <div class="dropdown">
                            <button class=" dropdown-toggle" type="button" id="dropdownMenuButton"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa-solid fa-ellipsis" style="color: #a0aec0;"></i>
                            </button>
                            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                @can('view-' . str_slug('Booking'))
                                    {{-- <a href="" data-toggle="modal" data-target="#booking"
                                    class="dropdown-item"
                                    title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Booking') }}">
                                    View
                                </a> --}}
                                    {{-- <a href="{{ url('/booking/booking/' . $item->id) }}"
                                    class="dropdown-item"
                                    title="View {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Booking') }}">
                                    View
                                </a> --}}
                                @endcan
                                @can('edit-' . str_slug('Booking'))
                                    <a href="{{ url('/booking/booking/' . $item->id . '/edit') }}"
                                        class="dropdown-item"
                                        title="Edit {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Booking') }}">
                                        <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                                        {{ __('action') }}
                                    </a>
                                @endcan
                                @if ($item->status == 0)
                                    {{-- <button class="dropdown-item text-danger cancel-booking"
                                    data-booking-id="{{ $item->ids }}">
                                    {{ __('Cancel') }}
                                </button> --}}
                                    <button type="button"
                                        class="dropdown-item text-danger cancel-booking"
                                        data-toggle="modal" data-target="#cancelation"
                                        data-booking-id="{{ $item->ids }}"
                                        data-payment-method="{{ $item->payment_method }}">
                                        {{ __('Cancel') }}
                                    </button>
                                @else
                                    @can('delete-' . str_slug('Booking'))
                                        <form method="POST"
                                            action="{{ url('/booking/booking' . '/' . $item->id) }}"
                                            id="delete-form-{{ $item->id }}"
                                            accept-charset="UTF-8" style="display:inline">
                                            {{ method_field('DELETE') }}
                                            {{ csrf_field() }}
                                            <button type="button"
                                                class="dropdown-item btn-sm delete-btn"
                                                data-id="{{ $item->id }}"
                                                title="Delete {{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', 'Booking') }}">
                                                {{-- <i class="fa fa-trash-o" aria-hidden="true"></i>  --}}
                                                {{ __('delete') }}
                                            </button>
                                        </form>
                                    @endcan
                                @endif
                                {{-- <a class="dropdown-item" data-toggle="modal" data-target="#">Cancel</a> --}}

                            </div>
                        </div>

                    </td>
                @endif
            </tr>
        @empty
            <tr>
                <td colspan="10" class="text-center">
                    @if ($status == 'today')
                        {{ __('No bookings for today') }}
                    @elseif ($status == 'ongoing')
                        {{ __('No ongoing bookings yet') }}
                    @elseif ($status == 'upcoming')
                        {{ __('No upcoming bookings yet') }}
                    @elseif ($status == 'completed')
                        {{ __('No completed bookings yet') }}
                    @elseif ($status == 'canceled')
                        {{ __('No canceled bookings yet') }}
                    @else
                        {{ __('No bookings yet') }}
                    @endif
                </td>
            </tr>
        @endforelse
    </tbody>
</table>
<div style="display: flex; justify-content: space-between; align-items: center;">
    <div class="pagination-info">
        Showing {{ $booking->firstItem() }} to {{ $booking->lastItem() }} of
        {{ $booking->total() }} entries
    </div>
    <div class="pagination-wrapper"> {!! $booking->appends(['search' => Request::get('search'), 'status' => $status, 'date' => Request::get('date')])->render() !!} </div>
</div>
